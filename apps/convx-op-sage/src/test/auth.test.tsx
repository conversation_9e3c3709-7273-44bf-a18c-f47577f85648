import { describe, it, expect } from 'vitest';
import { render, screen } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import Auth from '../pages/Auth';

// Mock the @convx/auth module since it's a workspace dependency
vi.mock('@convx/auth', () => ({
  AuthPage: ({ title, subtitle }: { title: string; subtitle: string }) => (
    <div data-testid="auth-page">
      <h1>{title}</h1>
      <p>{subtitle}</p>
    </div>
  ),
}));

describe('Auth Page', () => {
  it('renders auth page with correct title and subtitle', () => {
    render(
      <BrowserRouter>
        <Auth />
      </BrowserRouter>
    );

    expect(screen.getByText('Welcome to CONVX OpSage')).toBeInTheDocument();
    expect(screen.getByText('Sign in to access your operational intelligence platform')).toBeInTheDocument();
  });
});
