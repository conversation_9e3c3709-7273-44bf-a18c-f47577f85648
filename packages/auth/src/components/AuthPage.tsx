import { useEffect } from 'react';
import { Authenticator } from '@aws-amplify/ui-react';
import '@aws-amplify/ui-react/styles.css';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAmplifyAuth } from '../hooks/useAmplifyAuth';

interface AuthPageProps {
  title?: string;
  subtitle?: string;
  logoComponent?: React.ReactNode;
  redirectTo?: string;
}

export function AuthPage({ 
  title = 'Welcome to CONVX',
  subtitle = 'Sign in to your account or create a new one',
  logoComponent,
  redirectTo = '/'
}: AuthPageProps) {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAmplifyAuth();

  useEffect(() => {
    // Redirect to intended destination or default route if user is already authenticated
    if (user) {
      const from = location.state?.from?.pathname || redirectTo;
      navigate(from, { replace: true });
    }
  }, [user, navigate, location.state, redirectTo]);

  const defaultLogo = (
    <div className="w-16 h-16 bg-blue-600 rounded-lg flex items-center justify-center mx-auto mb-4">
      <span className="text-white font-bold text-2xl">C</span>
    </div>
  );

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100">
      <div className="w-full max-w-md mx-4">
        <div className="text-center mb-8">
          {logoComponent || defaultLogo}
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            {title}
          </h1>
          <p className="text-gray-600">
            {subtitle}
          </p>
        </div>

        <Authenticator signUpAttributes={['email']} socialProviders={[]}>
          {() => {
            // This will redirect to intended page via useEffect when user is authenticated
            return <div>Redirecting...</div>;
          }}
        </Authenticator>
      </div>
    </div>
  );
}
